/* Super Admin Content Force Display
 * This file forces all content to be visible in the Super Admin Dashboard
 * to debug the blank content issue
 */

/* ===== FORCE DISPLAY ALL CONTENT ===== */

/* Reset any potential hiding styles */
* {
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force all major containers to display */
.dashboard-wrapper,
.dashboard-content,
.main-content-wrapper,
.super-admin-content-container,
.modern-main-content,
.container-fluid,
.modern-stats-grid,
.modern-content-grid,
.enhanced-dashboard-banner,
.modern-card,
.enhanced-stat-card,
.modern-section-header,
.modern-breadcrumb {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Force proper layout for flex containers */
.dashboard-wrapper {
  display: flex !important;
  flex-direction: column !important;
}

.dashboard-content {
  display: flex !important;
  flex: 1 !important;
}

.main-content-wrapper {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  margin-left: 280px !important;
  width: calc(100% - 280px) !important;
  padding: 20px !important;
  background-color: #f9fafb !important;
  min-height: calc(100vh - 70px) !important;
}

.super-admin-content-container {
  display: block !important;
  width: 100% !important;
  padding: 0 !important;
}

.modern-main-content {
  display: block !important;
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  background-color: transparent !important;
}

.container-fluid {
  display: block !important;
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  background-color: transparent !important;
}

/* Force grid displays */
.modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

.modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Force banner display */
.enhanced-dashboard-banner {
  display: block !important;
  width: 90% !important;
  max-width: 1800px !important;
  margin: 10px auto 2rem auto !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  position: relative !important;
  z-index: 1 !important;
  background-color: #359e04 !important;
  min-height: 200px !important;
}

/* Force card displays */
.modern-card,
.enhanced-stat-card,
.enhanced-stat-card-link {
  display: block !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  min-height: 150px !important;
}

/* Force section headers */
.modern-section-header {
  display: block !important;
  margin-bottom: 1.5rem !important;
  padding: 1rem !important;
  background-color: white !important;
  border-radius: 8px !important;
}

.modern-section-title {
  font-size: 1.75rem !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin: 0 !important;
}

.modern-section-subtitle {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
  margin-top: 0.25rem !important;
}

/* Debug styles - subtle borders for layout verification */
.main-content-wrapper {
  border: 1px solid rgba(255, 0, 0, 0.2) !important;
}

.modern-main-content {
  border: 1px solid rgba(0, 0, 255, 0.2) !important;
}

.container-fluid {
  border: 1px solid rgba(0, 255, 0, 0.2) !important;
}

.enhanced-dashboard-banner {
  border: 1px solid rgba(128, 0, 128, 0.2) !important;
}

.modern-stats-grid {
  border: 1px solid rgba(255, 165, 0, 0.2) !important;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .modern-main-content,
  .container-fluid {
    width: 95% !important;
  }

  .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }

  .modern-main-content,
  .container-fluid {
    width: 100% !important;
  }
}
